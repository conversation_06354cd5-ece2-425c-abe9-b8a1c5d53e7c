from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.config import settings

# إنشاء المحرك (Engine)
engine = create_engine(settings.DATABASE_URL)

# جلسة العمل (Session)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# قاعدة النماذج
Base = declarative_base()

# دالة للحصول على جلسة DB
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
