from sqlalchemy.orm import Session
from app.models.product import Product

def seed_products(db: Session):
    sample_products = [
        Product(name="Chocolate Cake", description="Rich chocolate flavor", price=25.0, image_url="https://via.placeholder.com/150"),
        Product(name="Baklava", description="Sweet layered pastry", price=15.0, image_url="https://via.placeholder.com/150"),
        Product(name="<PERSON>na<PERSON>", description="Middle Eastern dessert", price=20.0, image_url="https://via.placeholder.com/150"),
    ]

    for product in sample_products:
        exists = db.query(Product).filter_by(name=product.name).first()
        if not exists:
            db.add(product)

    db.commit()
