from fastapi import FastAPI
from app.config import settings
from app.routes import products
from app.db.database import Base, engine, SessionLocal
from app.db.seed import seed_products

app = FastAPI(
    title=settings.APP_NAME,
    version="1.0.0"
)

# إنشاء الجداول
Base.metadata.create_all(bind=engine)

# تشغيل الـ seed عند الإقلاع
with SessionLocal() as db:
    seed_products(db)

# ربط الراوتات
app.include_router(products.router)

@app.get("/")
def read_root():
    return {"message": f"Welcome to {settings.APP_NAME}!"}
