from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from typing import List
from app.db.database import get_db
from app import models, schemas

router = APIRouter(
    prefix="/products",
    tags=["Products"]
)

@router.get("/", response_model=List[schemas.product.ProductResponse])
def get_products(db: Session = Depends(get_db)):
    return db.query(models.product.Product).all()
